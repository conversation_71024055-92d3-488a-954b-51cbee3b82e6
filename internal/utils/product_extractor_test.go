package utils

import (
	"testing"

	"github.com/bwmarrin/discordgo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewProductIDExtractor(t *testing.T) {
	extractor := NewProductIDExtractor()

	assert.NotNil(t, extractor)
	assert.NotEmpty(t, extractor.patterns)
	assert.NotEmpty(t, extractor.productIDFields)

	// 验证默认模式
	expectedPatterns := []string{
		`(?i)asin[:\s]*([A-Z0-9]{10})`,
		`(?i)product[_\s]*id[:\s]*([A-Z0-9]+)`,
		`(?i)sku[:\s]*([A-Z0-9\-]+)`,
		`(?i)item[_\s]*id[:\s]*([A-Z0-9]+)`,
		`\b([A-Z0-9]{8,15})\b`,
	}
	assert.Equal(t, expectedPatterns, extractor.patterns)
}

func TestExtractWithRegex(t *testing.T) {
	extractor := NewProductIDExtractor()

	tests := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "Amazon ASIN",
			content:  "Check out this product: ASIN: B08N5WRWNW",
			expected: "B08N5WRWNW",
		},
		{
			name:     "Product ID",
			content:  "Product ID: ABC123DEF",
			expected: "ABC123DEF",
		},
		{
			name:     "SKU format",
			content:  "SKU: PROD-123-XYZ",
			expected: "PROD-123-XYZ",
		},
		{
			name:     "Item ID",
			content:  "Item_ID: ITEM789",
			expected: "ITEM789",
		},
		{
			name:     "Generic alphanumeric",
			content:  "Product code ABCD1234EF available",
			expected: "ABCD1234EF",
		},
		{
			name:     "No match",
			content:  "This is just regular text",
			expected: "",
		},
		{
			name:     "Case insensitive ASIN",
			content:  "asin: b08n5wrwnw",
			expected: "b08n5wrwnw",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractor.extractWithRegex(tt.content)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsProductIDField(t *testing.T) {
	extractor := NewProductIDExtractor()

	tests := []struct {
		name      string
		fieldName string
		expected  bool
	}{
		{
			name:      "Product ID field",
			fieldName: "product_id",
			expected:  true,
		},
		{
			name:      "ASIN field",
			fieldName: "asin",
			expected:  true,
		},
		{
			name:      "Chinese product ID",
			fieldName: "产品id",
			expected:  true,
		},
		{
			name:      "SKU field",
			fieldName: "sku",
			expected:  true,
		},
		{
			name:      "Non-product field",
			fieldName: "description",
			expected:  false,
		},
		{
			name:      "Partial match",
			fieldName: "my_product_id_field",
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractor.isProductIDField(tt.fieldName)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractFromMessage(t *testing.T) {
	extractor := NewProductIDExtractor()

	tests := []struct {
		name     string
		message  *discordgo.Message
		expected string
	}{
		{
			name: "Extract from content",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "Check this product: ASIN: B08N5WRWNW",
			},
			expected: "B08N5WRWNW",
		},
		{
			name: "Extract from embed fields",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "Product information:",
				Embeds: []*discordgo.MessageEmbed{
					{
						Fields: []*discordgo.MessageEmbedField{
							{
								Name:  "Product ID",
								Value: "PROD123",
							},
						},
					},
				},
			},
			expected: "PROD123",
		},
		{
			name: "No product ID found",
			message: &discordgo.Message{
				ID:        "123",
				ChannelID: "456",
				Content:   "Just regular text",
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := extractor.ExtractFromMessage(tt.message)
			require.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertDiscordMessageToMap(t *testing.T) {
	extractor := NewProductIDExtractor()

	message := &discordgo.Message{
		ID:        "123456789",
		ChannelID: "987654321",
		Content:   "Test message",
		Author: &discordgo.User{
			ID:       "user123",
			Username: "testuser",
		},
		Embeds: []*discordgo.MessageEmbed{
			{
				Title:       "Test Embed",
				Description: "Test Description",
				URL:         "https://example.com",
				Color:       0xFF0000,
				Fields: []*discordgo.MessageEmbedField{
					{
						Name:   "Field1",
						Value:  "Value1",
						Inline: true,
					},
				},
			},
		},
	}

	result := extractor.convertDiscordMessageToMap(message)

	assert.Equal(t, "123456789", result["id"])
	assert.Equal(t, "987654321", result["channel_id"])
	assert.Equal(t, "Test message", result["content"])

	author, ok := result["author"].(map[string]interface{})
	require.True(t, ok)
	assert.Equal(t, "user123", author["id"])
	assert.Equal(t, "testuser", author["username"])

	embeds, ok := result["embeds"].([]map[string]interface{})
	require.True(t, ok)
	require.Len(t, embeds, 1)

	embed := embeds[0]
	assert.Equal(t, "Test Embed", embed["title"])
	assert.Equal(t, "Test Description", embed["description"])
	assert.Equal(t, "https://example.com", embed["url"])
	assert.Equal(t, 0xFF0000, embed["color"])

	fields, ok := embed["fields"].([]map[string]interface{})
	require.True(t, ok)
	require.Len(t, fields, 1)

	field := fields[0]
	assert.Equal(t, "Field1", field["name"])
	assert.Equal(t, "Value1", field["value"])
	assert.Equal(t, true, field["inline"])
}

func TestAddPattern(t *testing.T) {
	extractor := NewProductIDExtractor()
	initialCount := len(extractor.patterns)

	extractor.AddPattern(`custom[:\s]*([A-Z0-9]+)`)

	assert.Len(t, extractor.patterns, initialCount+1)
	assert.Contains(t, extractor.patterns, `custom[:\s]*([A-Z0-9]+)`)
}

func TestAddProductIDField(t *testing.T) {
	extractor := NewProductIDExtractor()
	initialCount := len(extractor.productIDFields)

	extractor.AddProductIDField("custom_field")

	assert.Len(t, extractor.productIDFields, initialCount+1)
	assert.Contains(t, extractor.productIDFields, "custom_field")
}

func TestGetPatterns(t *testing.T) {
	extractor := NewProductIDExtractor()
	patterns := extractor.GetPatterns()

	// 验证返回的是副本，不是原始切片
	patterns[0] = "modified"
	assert.NotEqual(t, "modified", extractor.patterns[0])
}

func TestGetProductIDFields(t *testing.T) {
	extractor := NewProductIDExtractor()
	fields := extractor.GetProductIDFields()

	// 验证返回的是副本，不是原始切片
	fields[0] = "modified"
	assert.NotEqual(t, "modified", extractor.productIDFields[0])
}
