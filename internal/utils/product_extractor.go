package utils

import (
	"regexp"
	"strings"

	"zeka-go/internal/types"

	"github.com/bwmarrin/discordgo"
)

// ProductIDExtractor 产品ID提取工具
// 统一处理从Discord消息中提取ProductID的逻辑，避免代码重复
type ProductIDExtractor struct {
	// 常见的产品ID正则表达式模式
	patterns []string
	// 产品ID字段名称列表
	productIDFields []string
}

// NewProductIDExtractor 创建新的ProductID提取器
func NewProductIDExtractor() *ProductIDExtractor {
	return &ProductIDExtractor{
		patterns: []string{
			`(?i)asin[:\s]*([A-Z0-9]{10})`,         // Amazon ASIN
			`(?i)product[_\s]*id[:\s]*([A-Z0-9]+)`, // 通用Product ID
			`(?i)sku[:\s]*([A-Z0-9\-]+)`,           // SKU
			`(?i)item[_\s]*id[:\s]*([A-Z0-9]+)`,    // Item ID
			`\b([A-Z0-9]{8,15})\b`,                 // 8-15位字母数字组合
		},
		productIDFields: []string{
			"productid", "product_id", "asin", "id",
			"产品id", "商品id", "编号", "sku", "item_id",
		},
	}
}

// ExtractFromMessage 从Discord消息中提取ProductID
// 这是主要的提取方法，整合了多种提取策略
func (e *ProductIDExtractor) ExtractFromMessage(message *discordgo.Message) (string, error) {
	// 策略1: 使用ProductExtractor提取产品信息
	extractor := types.NewProductExtractor()
	messageData := e.convertDiscordMessageToMap(message)
	product := extractor.ExtractFromDiscordMessage(messageData, message.Content)

	if product.ProductID != "" {
		return product.ProductID, nil
	}

	// 策略2: 使用正则表达式从消息内容提取
	productID := e.extractWithRegex(message.Content)
	if productID != "" {
		return productID, nil
	}

	// 策略3: 从Embeds字段中提取
	for _, embed := range message.Embeds {
		if embed.Fields != nil {
			for _, field := range embed.Fields {
				if e.isProductIDField(strings.ToLower(field.Name)) {
					return field.Value, nil
				}
			}
		}
	}

	return "", nil
}

// ExtractWithRegex 使用正则表达式提取ProductID
func (e *ProductIDExtractor) extractWithRegex(content string) string {
	for _, pattern := range e.patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			return matches[1]
		}
	}
	return ""
}

// IsProductIDField 检查字段名是否为ProductID字段
func (e *ProductIDExtractor) isProductIDField(fieldName string) bool {
	for _, field := range e.productIDFields {
		if strings.Contains(fieldName, field) {
			return true
		}
	}
	return false
}

// ConvertDiscordMessageToMap 将Discord消息转换为map格式
// 用于与ProductExtractor兼容
func (e *ProductIDExtractor) convertDiscordMessageToMap(message *discordgo.Message) map[string]interface{} {
	messageMap := map[string]interface{}{
		"id":         message.ID,
		"channel_id": message.ChannelID,
		"content":    message.Content,
		"timestamp":  message.Timestamp,
	}

	if message.Author != nil {
		messageMap["author"] = map[string]interface{}{
			"id":       message.Author.ID,
			"username": message.Author.Username,
		}
	}

	if len(message.Embeds) > 0 {
		embeds := make([]map[string]interface{}, len(message.Embeds))
		for i, embed := range message.Embeds {
			embedMap := make(map[string]interface{})

			if embed.Title != "" {
				embedMap["title"] = embed.Title
			}
			if embed.Description != "" {
				embedMap["description"] = embed.Description
			}
			if embed.URL != "" {
				embedMap["url"] = embed.URL
			}
			if embed.Color != 0 {
				embedMap["color"] = embed.Color
			}

			if embed.Fields != nil {
				fields := make([]map[string]interface{}, len(embed.Fields))
				for j, field := range embed.Fields {
					fields[j] = map[string]interface{}{
						"name":   field.Name,
						"value":  field.Value,
						"inline": field.Inline,
					}
				}
				embedMap["fields"] = fields
			}

			embeds[i] = embedMap
		}
		messageMap["embeds"] = embeds
	}

	return messageMap
}

// AddPattern 添加自定义正则表达式模式
func (e *ProductIDExtractor) AddPattern(pattern string) {
	e.patterns = append(e.patterns, pattern)
}

// AddProductIDField 添加自定义ProductID字段名
func (e *ProductIDExtractor) AddProductIDField(fieldName string) {
	e.productIDFields = append(e.productIDFields, fieldName)
}

// GetPatterns 获取所有正则表达式模式
func (e *ProductIDExtractor) GetPatterns() []string {
	return append([]string(nil), e.patterns...)
}

// GetProductIDFields 获取所有ProductID字段名
func (e *ProductIDExtractor) GetProductIDFields() []string {
	return append([]string(nil), e.productIDFields...)
}
